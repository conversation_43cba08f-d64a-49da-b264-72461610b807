{"master": {"tasks": [{"id": 13, "title": "Import wildcard certificate and hosted zone into LambdasStack", "description": "Modify LambdasStack constructor to accept rootDomain, wildcardApiCert, and hostedZone props from the entry stack", "details": "Update infra/cloud-shared/lambdas/lambdas-stack.ts constructor to accept props: { rootDomain: string, wildcardApiCert: ICertificate, hostedZone: IHostedZone }. Reference existing LocalCertsStack.wildcardApiCert pattern from fargate-service-stack.ts. Use AWS CDK v2 latest stable (2.110.x+) with @aws-cdk/aws-apigatewayv2-alpha for HTTP API support. Import statements: import { HttpApi, HttpMethod, HttpRoute } from '@aws-cdk/aws-apigatewayv2-alpha'; import { HttpLambdaIntegration } from '@aws-cdk/aws-apigatewayv2-integrations-alpha';", "testStrategy": "Verify props are correctly passed from entry stack and accessible in LambdasStack. Test CDK synth to ensure no compilation errors and props are properly typed.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Update LambdasStack constructor to accept new props", "description": "Modify the constructor of LambdasStack in infra/cloud-shared/lambdas/lambdas-stack.ts to accept rootDomain (string), wildcardApiCert (ICertificate), and hostedZone (IHostedZone) as props.", "dependencies": [], "details": "Edit the TypeScript interface for LambdasStack props and update the constructor signature to include the new properties. Ensure type safety and compatibility with AWS CDK v2.", "status": "pending", "testStrategy": "Run TypeScript type checks and CDK synth to verify the constructor accepts and correctly types the new props."}, {"id": 2, "title": "Reference wildcard certificate and hosted zone from entry stack", "description": "Update the entry stack to pass the wildcardApiCert and hostedZone resources to LambdasStack, following the pattern used in fargate-service-stack.ts for LocalCertsStack.wildcardApiCert.", "dependencies": ["13.1"], "details": "Locate the entry stack where LambdasStack is instantiated. Import or reference the existing ACM certificate and Route53 hosted zone, and pass them as props to LambdasStack.", "status": "pending", "testStrategy": "Verify that the correct certificate and hosted zone are passed by inspecting the synthesized CloudFormation template and running CDK synth."}, {"id": 3, "title": "Update import statements for API Gateway v2 alpha modules", "description": "Ensure lambdas-stack.ts imports the required modules for HTTP API and Lambda integration from @aws-cdk/aws-apigatewayv2-alpha and @aws-cdk/aws-apigatewayv2-integrations-alpha.", "dependencies": ["13.1"], "details": "Add or update import statements for HttpApi, HttpMethod, HttpRoute, and HttpLambdaIntegration as specified in the implementation details.", "status": "pending", "testStrategy": "Run TypeScript build and CDK synth to confirm imports are correct and there are no compilation errors."}, {"id": 4, "title": "Refactor LambdasStack resource definitions to use new props", "description": "Update all resource definitions within LambdasStack that require rootDomain, wildcardApiCert, or hostedZone to use the new props instead of hardcoded or locally defined values.", "dependencies": ["13.1", "13.2"], "details": "Replace any direct references to certificates, domains, or hosted zones with references to the corresponding props. Ensure all constructs (e.g., API Gateway custom domains) use the injected resources.", "status": "pending", "testStrategy": "Run CDK synth and inspect the output to ensure resources are correctly parameterized and no hardcoded values remain."}, {"id": 5, "title": "Validate stack integration and deployment", "description": "Test the integration by synthesizing and deploying the updated LambdasStack to ensure all resources are created with the correct certificate and hosted zone.", "dependencies": ["13.4"], "details": "Perform a full CDK synth and deploy in a test environment. Check that the stack deploys without errors and that the resources reference the correct ACM certificate and Route53 hosted zone.", "status": "pending", "testStrategy": "Verify deployment success, inspect resource properties in the AWS Console, and confirm that the custom domain and certificate are correctly associated."}]}, {"id": 14, "title": "Configure Lambda function artifact path integration", "description": "Set up the shared-discovery-lambda Go binary artifact path using existing build pipeline integration", "details": "Reference existing Lambda function creation pattern in lambdas-stack.ts. Use aws-cdk-lib/aws-lambda Function construct with Go 1.22 runtime (Runtime.GO_1_X). Set code path to infra/cloud-shared/lambdas/discovery-lambda/main.go compiled binary. Configure handler as 'main', timeout 10 seconds, memory 256MB. Environment variables: LOG_LEVEL=INFO. Use existing build pipeline artifact resolution pattern from other Lambda functions in the stack.", "testStrategy": "Verify Lambda function deploys successfully with correct Go runtime and handler. Test function invocation locally using AWS CLI or CDK testing utilities.", "priority": "high", "dependencies": [13], "status": "pending", "subtasks": [{"id": 1, "title": "Review Existing Lambda Function Integration Patterns", "description": "Analyze the current Lambda function creation and artifact resolution patterns in lambdas-stack.ts to ensure consistency with existing build pipeline integration.", "dependencies": [], "details": "Identify how other Lambda functions in the stack resolve their Go binary artifact paths and how the build pipeline outputs are referenced in the CDK stack.", "status": "pending", "testStrategy": "Verify understanding by documenting the artifact path and integration approach used by at least one existing Lambda function."}, {"id": 2, "title": "Compile shared-discovery-lambda Go Binary", "description": "Set up the build process to compile infra/cloud-shared/lambdas/discovery-lambda/main.go into a Go binary suitable for Lambda deployment.", "dependencies": ["14.1"], "details": "Ensure the build pipeline produces the Go binary in the expected output directory, matching the artifact resolution pattern used by other Lambda functions.", "status": "pending", "testStrategy": "Confirm the compiled binary exists at the expected path after a pipeline run and is executable."}, {"id": 3, "title": "Integrate Compiled Artifact Path in CDK Stack", "description": "Update lambdas-stack.ts to reference the compiled shared-discovery-lambda Go binary artifact path using the aws-cdk-lib/aws-lambda Function construct.", "dependencies": ["14.2"], "details": "Set the code property to the compiled binary path, configure the handler as 'main', runtime as Go 1.22 (Runtime.GO_1_X), timeout to 10 seconds, memory to 256MB, and environment variable LOG_LEVEL=INFO.", "status": "pending", "testStrategy": "Deploy the stack and verify the Lambda function is created with the correct configuration and artifact."}, {"id": 4, "title": "Validate Build Pipeline Artifact Resolution", "description": "Ensure the build pipeline correctly resolves and provides the Lambda artifact for deployment, following the established pattern for other Lambda functions.", "dependencies": ["14.3"], "details": "Check that the artifact path used in the CDK stack matches the output from the build pipeline and that any required pipeline steps are present.", "status": "pending", "testStrategy": "Trigger a full pipeline run and confirm the Lambda artifact is available and referenced correctly in the deployment."}, {"id": 5, "title": "Test Lambda Deployment and Invocation", "description": "Verify the deployed Lambda function operates as expected with the integrated artifact path and configuration.", "dependencies": ["14.4"], "details": "Test deployment success, correct runtime and handler, environment variable presence, and function invocation using AWS CLI or CDK testing utilities.", "status": "pending", "testStrategy": "Invoke the Lambda function and check logs/output for correct behavior; ensure deployment and invocation tests pass."}]}, {"id": 15, "title": "Create HTTP API Gateway with Lambda integration", "description": "Implement HTTP API Gateway with default Lambda proxy integration for POST / route", "details": "Create HttpApi using @aws-cdk/aws-apigatewayv2-alpha. Configure HttpLambdaIntegration for POST / route with proxy integration enabled. Set CORS configuration: allowOrigins: ['*'], allowMethods: [HttpMethod.POST, HttpMethod.OPTIONS], allowHeaders: ['Content-Type', 'Authorization']. Configure default stage with throttling: burstLimit: 1000, rateLimit: 500. Use payloadFormatVersion: '2.0' for Lambda proxy integration v2 format.", "testStrategy": "Test API Gateway creation and Lambda integration using CDK synth. Verify POST requests to / route successfully invoke Lambda function. Test CORS headers are properly set.", "priority": "high", "dependencies": [14], "status": "pending", "subtasks": [{"id": 1, "title": "Define and Configure HttpApi Resource", "description": "Create an HttpApi resource using @aws-cdk/aws-apigatewayv2-alpha with the required base configuration.", "dependencies": [], "details": "Instantiate the HttpApi construct in the CDK stack. Set the API name, description, and any initial configuration required for the HTTP API resource.", "status": "pending", "testStrategy": "Run 'cdk synth' to verify the HttpApi resource is defined in the generated CloudFormation template."}, {"id": 2, "title": "Implement Lambda Function for Proxy Integration", "description": "Create the Lambda function that will serve as the backend for the API Gateway POST / route.", "dependencies": ["15.1"], "details": "Define the Lambda function using AWS CDK, specifying runtime, handler, and code location. Ensure the function is compatible with payloadFormatVersion 2.0 for proxy integration.", "status": "pending", "testStrategy": "Deploy the stack and invoke the Lambda function directly to confirm it executes and returns a valid response."}, {"id": 3, "title": "Configure Lambda Proxy Integration for POST Route", "description": "Set up HttpLambdaIntegration for the POST / route with proxy integration enabled and payloadFormatVersion 2.0.", "dependencies": ["15.2"], "details": "Use HttpLambdaIntegration to connect the POST / route of the HttpApi to the Lambda function. Ensure proxy integration is enabled and payloadFormatVersion is set to '2.0'.", "status": "pending", "testStrategy": "Deploy the stack and send a POST request to the / route. Verify that the Lambda function is invoked and the response is returned through API Gateway."}, {"id": 4, "title": "Set CORS and Throttling Configuration", "description": "Configure CORS settings and default stage throttling for the HttpApi.", "dependencies": ["15.3"], "details": "Set allowOrigins to ['*'], allowMethods to [HttpMethod.POST, HttpMethod.OPTIONS], and allowHeaders to ['Content-Type', 'Authorization']. Configure the default stage with burstLimit: 1000 and rateLimit: 500.", "status": "pending", "testStrategy": "Send OPTIONS and POST requests with various origins and headers. Confirm CORS headers are present and throttling limits are enforced."}, {"id": 5, "title": "Validate Deployment and Integration", "description": "Test the complete API Gateway and Lambda integration, including CORS and throttling.", "dependencies": ["15.4"], "details": "Deploy the stack and use tools like curl or Postman to send POST requests to the / route. Verify Lambda invocation, CORS headers, and throttling behavior. Use 'cdk synth' and CloudWatch logs for validation.", "status": "pending", "testStrategy": "Perform end-to-end tests: 1) POST / invokes Lambda and returns expected response, 2) CORS headers are correct, 3) Throttling limits are enforced, 4) No errors in CloudWatch logs."}]}, {"id": 16, "title": "Implement health check mock integration", "description": "Add GET /health route with API Gateway mock integration returning 200 OK without Lambda invocation", "details": "Create HttpRoute for GET /health using HttpMockIntegration from @aws-cdk/aws-apigatewayv2-integrations-alpha. Configure mock response: statusCode: 200, responseParameters: { 'method.response.header.Content-Type': 'application/json' }, responseTemplates: { 'application/json': '{}' }. No authorizer required. This avoids Lambda cold start costs for health checks.", "testStrategy": "Test GET /health returns 200 status with empty JSON body. Verify no Lambda function is invoked. Test response time is consistently under 50ms.", "priority": "medium", "dependencies": [15], "status": "pending", "subtasks": [{"id": 1, "title": "Define GET /health Route in API Gateway", "description": "Add a new GET /health route to the HTTP API Gateway using AWS CDK.", "dependencies": [], "details": "Use the AWS CDK to define an HttpRoute for the /health path with the GET method, ensuring it is distinct from other routes in the API.", "status": "pending", "testStrategy": "Verify that the route appears in the synthesized CloudFormation template and is accessible via the API Gateway console."}, {"id": 2, "title": "Configure HttpMockIntegration for /health Route", "description": "Attach an HttpMockIntegration to the GET /health route to enable mock responses without backend invocation.", "dependencies": ["16.1"], "details": "Utilize @aws-cdk/aws-apigatewayv2-integrations-alpha to configure HttpMockIntegration for the route, ensuring no Lambda or backend is triggered.", "status": "pending", "testStrategy": "Check that the integration type for the route is MOCK and that no Lambda or backend resources are referenced."}, {"id": 3, "title": "Set Mock Response Parameters and Templates", "description": "Configure the mock integration to return a 200 status, set Content-Type to application/json, and respond with an empty JSON object.", "dependencies": ["16.2"], "details": "Set integration response parameters: statusCode: 200, responseParameters: { 'method.response.header.Content-Type': 'application/json' }, responseTemplates: { 'application/json': '{}' }.", "status": "pending", "testStrategy": "Deploy and invoke GET /health; confirm the response is 200 with Content-Type: application/json and body {}."}, {"id": 4, "title": "Disable Authorization for Health Check Route", "description": "Ensure the GET /health route does not require any authorizer or authentication.", "dependencies": ["16.3"], "details": "Explicitly configure the route to have no authorizer, making it publicly accessible for health checks.", "status": "pending", "testStrategy": "Test GET /health without any authentication headers and confirm successful response."}, {"id": 5, "title": "Validate Performance and No Lambda Invocation", "description": "Test that GET /health consistently returns 200 within 50ms and verify that no Lambda function is invoked.", "dependencies": ["16.4"], "details": "Use monitoring tools or logs to confirm no Lambda invocations occur for health checks and measure response latency.", "status": "pending", "testStrategy": "Run repeated GET /health requests, check CloudWatch logs for absence of Lambda invocations, and ensure response times are under 50ms."}]}, {"id": 17, "title": "Configure custom domain with wildcard certificate", "description": "Set up custom domain discovery.api.<rootDomain> using the existing wildcard ACM certificate", "details": "Use DomainName construct from @aws-cdk/aws-apigatewayv2-alpha. Configure domainName: `discovery.api.${props.rootDomain}`, certificate: props.wildcardApiCert. Create ApiMapping to associate custom domain with HTTP API default stage. Set securityPolicy: SecurityPolicy.TLS_1_2 for compliance. Reference pattern from fargate-service-stack.ts custom domain configuration.", "testStrategy": "Verify custom domain is created successfully and SSL certificate is properly associated. Test domain resolves correctly and serves HTTPS traffic. Validate certificate chain using SSL testing tools.", "priority": "high", "dependencies": [16], "status": "pending", "subtasks": [{"id": 1, "title": "Retrieve and reference existing wildcard ACM certificate", "description": "Obtain the existing wildcard ACM certificate and ensure it is accessible within the CDK stack for use with the custom domain.", "dependencies": [], "details": "Use aws-cdk-lib/aws-certificatemanager to reference the certificate by ARN (e.g., Certificate.fromCertificateArn). Confirm that the certificate covers discovery.api.<rootDomain> and is validated.", "status": "pending", "testStrategy": "Verify that the certificate is correctly referenced in the CDK stack and that its domain coverage includes the intended subdomain."}, {"id": 2, "title": "Configure API Gateway custom domain using DomainName construct", "description": "Set up the custom domain discovery.api.<rootDomain> for the HTTP API using the DomainName construct from @aws-cdk/aws-apigatewayv2-alpha.", "dependencies": ["17.1"], "details": "Instantiate DomainName with domainName: `discovery.api.${props.rootDomain}`, certificate: props.wildcardApiCert, and securityPolicy: SecurityPolicy.TLS_1_2. Reference the configuration pattern from fargate-service-stack.ts.", "status": "pending", "testStrategy": "Deploy the stack and confirm that the custom domain resource is created with the correct certificate and security policy."}, {"id": 3, "title": "Create ApiMapping to associate custom domain with HTTP API default stage", "description": "Map the custom domain to the HTTP API's default stage using ApiMapping.", "dependencies": ["17.2"], "details": "Use the ApiMapping construct to associate the DomainName with the HTTP API's default stage, ensuring requests to the custom domain are routed to the API.", "status": "pending", "testStrategy": "Test that requests to discovery.api.<rootDomain> are routed to the correct API stage and endpoints."}, {"id": 4, "title": "Update DNS records to point custom domain to API Gateway", "description": "Configure DNS to route discovery.api.<rootDomain> traffic to the API Gateway custom domain endpoint.", "dependencies": ["17.3"], "details": "Add a CNAME or A/ALIAS record in Route 53 or the domain registrar, pointing discovery.api.<rootDomain> to the API Gateway domain name or CloudFront distribution as appropriate.", "status": "pending", "testStrategy": "Use dig or nslookup to verify DNS resolution, and ensure the domain points to the correct AWS endpoint."}, {"id": 5, "title": "Validate custom domain and SSL configuration", "description": "Test that the custom domain serves HTTPS traffic with the correct certificate and that the certificate chain is valid.", "dependencies": ["17.4"], "details": "Use curl or SSL testing tools to verify successful HTTPS connections, correct certificate presentation, and valid certificate chain for discovery.api.<rootDomain>.", "status": "pending", "testStrategy": "Perform end-to-end tests: access the domain via HTTPS, check for valid SSL handshake, and confirm the certificate matches the wildcard ACM certificate."}]}, {"id": 18, "title": "Create Route 53 A-record for custom domain", "description": "Add Route 53 A-record pointing discovery.api.<rootDomain> to API Gateway custom domain", "details": "Use ARecord construct from aws-cdk-lib/aws-route53. Configure recordName: 'discovery.api', zone: props.hostedZone, target: RecordTarget.fromAlias(new ApiGatewayv2DomainProperties(customDomain.regionalDomainName, customDomain.regionalHostedZoneId)). Import ApiGatewayv2DomainProperties from aws-cdk-lib/aws-route53-targets. Set TTL to 300 seconds for faster DNS propagation during deployments.", "testStrategy": "Verify DNS record is created and propagates correctly using dig or nslookup. Test domain resolution from multiple geographic locations. Validate A-record points to correct API Gateway regional endpoint.", "priority": "high", "dependencies": [17], "status": "pending", "subtasks": [{"id": 1, "title": "Import Required CDK Constructs and Properties", "description": "Import the ARecord construct from aws-cdk-lib/aws-route53 and ApiGatewayv2DomainProperties from aws-cdk-lib/aws-route53-targets for use in the stack.", "dependencies": [], "details": "Ensure the CDK stack file includes imports for ARecord, RecordTarget, and ApiGatewayv2DomainProperties to enable creation of the Route 53 alias record targeting the API Gateway custom domain.", "status": "pending", "testStrategy": "Verify that the CDK stack compiles without import errors and that all required constructs are available for use."}, {"id": 2, "title": "Reference Hosted Zone and API Gateway Custom Domain", "description": "Retrieve or reference the existing Route 53 hosted zone and the API Gateway custom domain properties (regionalDomainName and regionalHostedZoneId).", "dependencies": ["18.1"], "details": "Use HostedZone.fromLookup or similar method to get the hosted zone object. Ensure the customDomain object exposes regionalDomainName and regionalHostedZoneId for use in the ARecord target.", "status": "pending", "testStrategy": "Confirm that the hosted zone and custom domain properties are correctly referenced and available in the stack context."}, {"id": 3, "title": "Create Route 53 A-Record for discovery.api Subdomain", "description": "Define a new ARecord in Route 53 with recordName 'discovery.api', pointing to the API Gateway custom domain using RecordTarget.fromAlias and ApiGatewayv2DomainProperties.", "dependencies": ["18.2"], "details": "Instantiate the ARecord construct with the specified recordName, zone, and target. Use RecordTarget.fromAlias(new ApiGatewayv2DomainProperties(customDomain.regionalDomainName, customDomain.regionalHostedZoneId)).", "status": "pending", "testStrategy": "Deploy the stack and verify that the A-record is created in the correct hosted zone with the expected alias target."}, {"id": 4, "title": "Configure TTL for DNS Propagation", "description": "Set the TTL (time-to-live) for the A-record to 300 seconds to enable faster DNS propagation during deployments.", "dependencies": ["18.3"], "details": "Specify the ttl property as Duration.seconds(300) or equivalent in the ARecord construct configuration.", "status": "pending", "testStrategy": "Check the created DNS record in the Route 53 console or via AWS CLI to ensure the TTL is set to 300 seconds."}, {"id": 5, "title": "Validate DNS Record Creation and Propagation", "description": "Test that the discovery.api.<rootDomain> A-record resolves to the correct API Gateway endpoint from multiple locations and propagates as expected.", "dependencies": ["18.4"], "details": "Use tools like dig or nslookup to query the DNS record after deployment. Confirm that the record resolves to the API Gateway regional endpoint and that propagation occurs within the expected TTL window.", "status": "pending", "testStrategy": "Perform DNS lookups from different geographic locations and verify the A-record points to the correct API Gateway endpoint. Document results and address any propagation issues."}]}, {"id": 19, "title": "Configure CloudWatch access logging", "description": "Set up CloudWatch access log group and configure API Gateway stage logging", "details": "Create LogGroup using aws-cdk-lib/aws-logs with logGroupName: `/aws/apigateway/discovery-api-${props.rootDomain}`, retention: RetentionDays.ONE_MONTH. Configure HttpStage accessLogSettings: { destinationArn: logGroup.logGroupArn, format: AccessLogFormat.jsonWithStandardFields() }. Include requestId, ip, userAgent, requestTime, httpMethod, resourcePath, status, responseLength, responseTime in log format. Reference fargate-service-stack.ts logging pattern.", "testStrategy": "Verify log group is created and API Gateway writes access logs. Test log entries contain all required fields and are properly formatted JSON. Monitor log ingestion rate and verify no log delivery errors.", "priority": "medium", "dependencies": [18], "status": "pending", "subtasks": [{"id": 1, "title": "Create CloudWatch Log Group for API Gateway Access Logs", "description": "Provision a CloudWatch Log Group using aws-cdk-lib/aws-logs with a name pattern `/aws/apigateway/discovery-api-${props.rootDomain}` and set retention to one month.", "dependencies": [], "details": "Use the LogGroup construct in CDK to create the log group with the specified name and retention policy. Ensure the log group is created before configuring API Gateway logging.", "status": "pending", "testStrategy": "Verify that the log group is created in the AWS Console with the correct name and retention period."}, {"id": 2, "title": "Grant API Gateway Permissions to Write to Log Group", "description": "Configure necessary IAM permissions so that API Gateway can write access logs to the created CloudWatch Log Group.", "dependencies": ["19.1"], "details": "Attach a resource policy or IAM role allowing API Gateway to put log events into the specified log group. Reference AWS documentation for required permissions.", "status": "pending", "testStrategy": "Check that API Gateway is able to deliver logs to the log group without permission errors."}, {"id": 3, "title": "Configure API Gateway Stage Access Log Settings", "description": "Set up the API Gateway HTTP stage to use the log group for access logging, specifying the destination ARN and log format.", "dependencies": ["19.2"], "details": "Use the accessLogSettings property in the HttpStage construct to set destinationArn to the log group ARN and format to AccessLogFormat.jsonWithStandardFields().", "status": "pending", "testStrategy": "Deploy the stack and verify that the API Gateway stage is configured with the correct access log settings."}, {"id": 4, "title": "Customize Access Log Format to Include Required Fields", "description": "Ensure the access log format includes requestId, ip, userAgent, requestTime, httpMethod, resourcePath, status, responseLength, and responseTime as JSON fields.", "dependencies": ["19.3"], "details": "Modify the access log format string or use AccessLogFormat.jsonWithStandardFields() with custom fields as needed to match the required logging pattern.", "status": "pending", "testStrategy": "Send test requests and verify that log entries in CloudWatch contain all specified fields in valid JSON format."}, {"id": 5, "title": "Validate Logging Pattern and Monitor Log Delivery", "description": "Reference the logging pattern from fargate-service-stack.ts and monitor log ingestion rate and delivery errors.", "dependencies": ["19.4"], "details": "Compare the configured log format with the pattern used in fargate-service-stack.ts for consistency. Set up monitoring for log ingestion and check for delivery errors.", "status": "pending", "testStrategy": "Review CloudWatch metrics and error logs to ensure logs are ingested at expected rates and no delivery errors occur."}]}, {"id": 20, "title": "Create WAFv2 WebACL with rate limiting rules", "description": "Implement WAFv2 WebACL with IP-based rate limiting to prevent abuse and DoS attacks", "details": "Create CfnWebACL using aws-cdk-lib/aws-wafv2. Configure scope: 'REGIONAL', rules: [{ name: 'RateLimitRule', priority: 1, statement: { rateBasedStatement: { limit: 200, aggregateKeyType: 'IP' } }, action: { block: {} } }]. Set defaultAction: { allow: {} }. Add CloudWatch metrics enabled: true. Use 5-minute evaluation window (300 seconds) matching requirement of 200 req/5 min/IP. Consider adding geo-blocking rule if needed for security.", "testStrategy": "Test rate limiting by sending >200 requests from single IP within 5 minutes and verify 429 responses. Monitor WAF metrics in CloudWatch. Verify legitimate traffic under limit passes through successfully.", "priority": "high", "dependencies": [19], "status": "pending", "subtasks": [{"id": 1, "title": "Define Rate Limiting Requirements and Patterns", "description": "Specify the IP-based rate limiting threshold (200 requests per 5 minutes per IP) and determine if any URL path or resource-specific regex patterns are needed for targeted protection.", "dependencies": [], "details": "Review application endpoints and security requirements. Decide if rate limiting should apply globally or to specific API paths using regex pattern sets. Document the required evaluation window and request limit.", "status": "pending", "testStrategy": "Validate requirements with stakeholders and confirm that the defined patterns and thresholds align with security objectives."}, {"id": 2, "title": "Implement WAFv2 WebACL with Rate-Based Rule in AWS CDK", "description": "Create a WAFv2 WebACL using aws-cdk-lib/aws-wafv2, configuring a rate-based rule with IP aggregation and a 5-minute (300 seconds) evaluation window.", "dependencies": ["20.1"], "details": "Use the CfnWebACL construct to define the WebACL. Set scope to 'REGIONAL', add a rule named 'RateLimitRule' with priority 1, and configure the rateBasedStatement with limit: 200 and aggregateKeyType: 'IP'. Set defaultAction to allow.", "status": "pending", "testStrategy": "Run CDK synth and deploy to verify the WebACL is created with the correct configuration."}, {"id": 3, "title": "Enable CloudWatch Metrics and Logging for WebACL", "description": "Configure the WebACL to enable CloudWatch metrics and logging for monitoring rule matches and rate limiting events.", "dependencies": ["20.2"], "details": "Set CloudWatchMetricsEnabled to true in the WebACL configuration. Optionally, configure logging destinations for detailed request logs.", "status": "pending", "testStrategy": "Check CloudWatch for the creation of relevant metrics and verify that logs are being generated for rate-limited requests."}, {"id": 4, "title": "Associate WebACL with Target AWS Resource", "description": "Attach the configured WebACL to the appropriate AWS resource (e.g., API Gateway or Application Load Balancer) to enforce rate limiting.", "dependencies": ["20.3"], "details": "Use the AWS CDK or AWS Console to associate the WebACL with the intended resource. Ensure the association is active and covers all relevant endpoints.", "status": "pending", "testStrategy": "Send requests to the protected resource and confirm that the WebACL is applied by observing WAF logs and metrics."}, {"id": 5, "title": "Test and Validate Rate Limiting and Optional Geo-Blocking", "description": "Perform functional testing to ensure the rate limiting rule blocks requests exceeding the threshold and consider adding a geo-blocking rule if required.", "dependencies": ["20.4"], "details": "Simulate traffic exceeding 200 requests per 5 minutes from a single IP and verify 429 responses. If geo-blocking is needed, add a rule to restrict traffic from specific countries.", "status": "pending", "testStrategy": "Automate tests to send bursts of requests and monitor for correct blocking behavior. Review CloudWatch metrics and logs to confirm rule effectiveness."}]}, {"id": 21, "title": "Associate WAF WebACL with API Gateway stage", "description": "Link the WAFv2 WebACL to the HTTP API Gateway stage for traffic filtering", "details": "Create CfnWebACLAssociation using aws-cdk-lib/aws-wafv2. Configure resourceArn: httpApi.defaultStage.stageArn, webAclArn: webAcl.attrArn. Ensure association is created after both API Gateway stage and WebACL exist. Add dependency using addDependsOn() if needed. This enables WAF filtering on all API requests before they reach Lambda.", "testStrategy": "Verify WAF association is active by checking AWS console or CLI. Test that rate-limited requests return 403/429 status codes. Confirm WAF metrics appear in CloudWatch for blocked and allowed requests.", "priority": "high", "dependencies": [20], "status": "pending", "subtasks": [{"id": 1, "title": "Retrieve ARNs for API Gateway Stage and WAFv2 WebACL", "description": "Obtain the Amazon Resource Names (ARNs) for both the API Gateway stage and the WAFv2 WebACL to be associated.", "dependencies": [], "details": "Use CDK constructs or CloudFormation outputs to get httpApi.defaultStage.stageArn and webAcl.attrArn as required for association.", "status": "pending", "testStrategy": "Verify that both ARNs are correctly resolved and available before proceeding to association."}, {"id": 2, "title": "Create CfnWebACLAssociation Resource in CDK", "description": "Define a new aws_wafv2.CfnWebACLAssociation resource in the AWS CDK stack to link the WebACL to the API Gateway stage.", "dependencies": ["21.1"], "details": "Configure the CfnWebACLAssociation with resourceArn set to the API Gateway stage ARN and webAclArn set to the WebACL ARN.", "status": "pending", "testStrategy": "Check that the synthesized CloudFormation template includes the correct association resource with the expected ARNs."}, {"id": 3, "title": "Ensure Proper Resource Dependencies", "description": "Guarantee that the WebACL association is created only after both the API Gateway stage and the WebACL exist.", "dependencies": ["21.2"], "details": "Use addDependsOn() or CDK dependency constructs to explicitly set dependencies between the association, API Gateway stage, and WebACL resources.", "status": "pending", "testStrategy": "Review the CloudFormation dependency graph to confirm correct resource creation order."}, {"id": 4, "title": "Deploy and Validate WAF Association", "description": "Deploy the CDK stack and confirm that the WAFv2 WebACL is successfully associated with the API Gateway stage.", "dependencies": ["21.3"], "details": "Deploy using CDK CLI. After deployment, verify the association in the AWS Console or via AWS CLI using 'get-web-acl-for-resource'.", "status": "pending", "testStrategy": "Check that the API Gateway stage lists the WebACL association and that WAF metrics appear in CloudWatch."}, {"id": 5, "title": "Test WAF Filtering on API Gateway", "description": "Test that the WAFv2 WebACL is actively filtering requests to the API Gateway stage as intended.", "dependencies": ["21.4"], "details": "Send requests that should be blocked (e.g., exceeding rate limits) and verify 403/429 responses. Confirm allowed requests pass through. Monitor CloudWatch for WAF metrics.", "status": "pending", "testStrategy": "Document test results showing blocked and allowed requests, and confirm WAF metrics reflect the traffic."}]}, {"id": 22, "title": "Configure CloudWatch alarms for monitoring", "description": "Set up CloudWatch alarms for 5xx errors, high latency, and WAF blocks", "details": "Create Alarm constructs using aws-cdk-lib/aws-cloudwatch. Configure alarms: 1) 5xx errors: metric 'AWS/ApiGateway/5XXError', threshold: 5 errors in 5 minutes, 2) High latency: metric 'AWS/ApiGateway/Latency', threshold: 2000ms P95, 3) WAF blocks: metric 'AWS/WAFV2/BlockedRequests', threshold: 50 blocks in 5 minutes. Set alarm actions to SNS topic if available. Use MetricFilter for custom log-based metrics if needed.", "testStrategy": "Trigger test conditions to verify alarms fire correctly. Test 5xx alarm by causing Lambda errors, latency alarm by adding artificial delays, WAF alarm by exceeding rate limits. Verify alarm states transition properly.", "priority": "medium", "dependencies": [21], "status": "pending", "subtasks": [{"id": 1, "title": "Define CloudWatch Alarm for 5xx Errors", "description": "Create a CloudWatch Alarm construct in aws-cdk-lib/aws-cloudwatch to monitor the 'AWS/ApiGateway/5XXError' metric with a threshold of 5 errors within 5 minutes.", "dependencies": [], "details": "Configure the alarm with the specified metric, threshold, and evaluation period. Ensure the alarm triggers when 5xx errors exceed the threshold in the defined window.", "status": "pending", "testStrategy": "Induce Lambda or backend errors to generate 5xx responses and verify the alarm transitions to ALARM state."}, {"id": 2, "title": "Define CloudWatch Alarm for High Latency", "description": "Create a CloudWatch Alarm construct to monitor the 'AWS/ApiGateway/Latency' metric, specifically the P95 percentile, with a threshold of 2000ms.", "dependencies": [], "details": "Set up the alarm to evaluate the P95 latency metric and trigger if the value exceeds 2000ms within the evaluation period.", "status": "pending", "testStrategy": "Introduce artificial delays in API responses to exceed the latency threshold and confirm the alarm fires as expected."}, {"id": 3, "title": "Define CloudWatch Alarm for WAF Blocked Requests", "description": "Create a CloudWatch Alarm construct to monitor the 'AWS/WAFV2/BlockedRequests' metric with a threshold of 50 blocks in 5 minutes.", "dependencies": [], "details": "Configure the alarm to trigger when the number of blocked requests by WAF exceeds 50 within a 5-minute window.", "status": "pending", "testStrategy": "Simulate requests that violate WAF rules to exceed the block threshold and verify the alarm state transitions."}, {"id": 4, "title": "Configure Alarm Actions and SNS Notifications", "description": "Set up alarm actions to notify an SNS topic when any of the defined alarms are triggered.", "dependencies": ["22.1", "22.2", "22.3"], "details": "Associate each alarm with an SNS topic for notifications. Ensure the SNS topic exists and is properly configured for alarm actions.", "status": "pending", "testStrategy": "Trigger each alarm and confirm that notifications are sent to the SNS topic subscribers."}, {"id": 5, "title": "Implement Custom MetricFilters for Log-Based Metrics (if needed)", "description": "Create MetricFilters using aws-cdk-lib/aws-logs for any custom log-based metrics required for monitoring, and integrate them with CloudWatch alarms as necessary.", "dependencies": [], "details": "Define MetricFilters to extract relevant metrics from CloudWatch Logs, and connect these metrics to new or existing alarms if default metrics are insufficient.", "status": "pending", "testStrategy": "Generate log entries matching the filter pattern and verify that the custom metric and associated alarm behave as expected."}]}, {"id": 23, "title": "Update mobile app configuration for new endpoint", "description": "Modify mobile app configuration to use new discovery.api.<rootDomain> endpoint while maintaining Function URL fallback", "details": "Update mobile app constants: export const DISCOVERY_URL = 'https://discovery.api.gethero.com'; Implement fallback logic: try new endpoint first, on failure (network error, 5xx) retry with old Function URL. Add exponential backoff for 429 responses: initial delay 1s, max delay 30s, max retries 3. Use fetch API with timeout: 10 seconds. Handle errors gracefully with user-friendly messages. Consider feature flag for gradual rollout.", "testStrategy": "Test mobile app with new endpoint in staging environment. Verify fallback works when new endpoint is unavailable. Test rate limiting handling with proper retry behavior. Validate error handling and user experience.", "priority": "high", "dependencies": [22], "status": "pending", "subtasks": [{"id": 1, "title": "Update Mobile App Constants for New Endpoint", "description": "Modify the mobile app configuration to set DISCOVERY_URL to the new endpoint (https://discovery.api.<rootDomain>).", "dependencies": [], "details": "Update the relevant constants or configuration files in the codebase to reference the new discovery API endpoint as the primary URL.", "status": "pending", "testStrategy": "Verify that the app attempts to connect to the new endpoint in the staging environment and that requests are routed correctly."}, {"id": 2, "title": "Implement Endpoint Fallback Logic", "description": "Add logic to attempt the new endpoint first and, on network error or 5xx response, retry with the old Function URL.", "dependencies": ["23.1"], "details": "Use fetch API to try the new endpoint; if a network error or server error (5xx) occurs, automatically retry the request using the previous Function URL as a fallback.", "status": "pending", "testStrategy": "Simulate endpoint failures and confirm that the fallback mechanism triggers and successfully retries with the old URL."}, {"id": 3, "title": "Add Exponential Backoff for 429 Responses", "description": "Implement exponential backoff logic for handling HTTP 429 (Too Many Requests) responses, starting with a 1s delay, doubling up to 30s, with a maximum of 3 retries.", "dependencies": ["23.2"], "details": "Detect 429 responses and apply exponential backoff: initial delay 1 second, double each retry, cap at 30 seconds, and stop after 3 attempts.", "status": "pending", "testStrategy": "Test with mocked 429 responses to ensure the backoff timing and retry limits are correctly enforced."}, {"id": 4, "title": "Integrate Fetch API Timeout and Error Handling", "description": "Ensure all fetch requests use a 10-second timeout and handle errors gracefully with user-friendly messages.", "dependencies": ["23.3"], "details": "Wrap fetch calls with timeout logic; display clear, actionable error messages to users for timeouts, network errors, and other failures.", "status": "pending", "testStrategy": "Test under poor network conditions and forced timeouts to validate error handling and user experience."}, {"id": 5, "title": "Implement Feature Flag for Gradual Rollout", "description": "Add a feature flag to control rollout of the new endpoint configuration, allowing gradual enablement for users.", "dependencies": ["23.4"], "details": "Integrate a feature flag system to toggle the use of the new endpoint, enabling staged deployment and rollback if needed.", "status": "pending", "testStrategy": "Toggle the feature flag in staging and production to confirm correct behavior and safe rollout control."}]}, {"id": 24, "title": "Implement comprehensive integration testing", "description": "Create end-to-end tests covering all API routes, error scenarios, and security features", "details": "Create test suite using Jest or similar framework. Test scenarios: 1) Valid POST / with email returns correct userPoolUrl/clientId, 2) Invalid email format returns 400, 3) GET /health returns 200 {}, 4) Rate limiting triggers 429 after threshold, 5) CORS headers present, 6) SSL certificate validation, 7) DNS resolution. Use supertest for HTTP testing, aws-sdk for direct API calls. Mock <PERSON>da responses for isolated API Gateway testing.", "testStrategy": "Run full test suite in CI/CD pipeline. Achieve >90% code coverage. Test against deployed staging environment. Include performance tests to verify <200ms response time requirement.", "priority": "medium", "dependencies": [23], "status": "pending", "subtasks": [{"id": 1, "title": "Design comprehensive test cases for all API routes and scenarios", "description": "Identify and document all API endpoints, valid and invalid input scenarios, error conditions, and security features to be covered by integration tests.", "dependencies": [], "details": "Review API specifications and requirements to ensure all routes (e.g., POST /, GET /health) and scenarios (valid/invalid input, rate limiting, CORS, SSL, DNS) are included. Define expected outcomes for each scenario.", "status": "pending", "testStrategy": "Cross-check test cases against API documentation and requirements to ensure completeness and relevance."}, {"id": 2, "title": "Set up integration testing environment and tools", "description": "Configure the testing framework (e.g., Jest), HTTP testing utilities (e.g., supertest), and AWS SDK for direct API calls. Prepare mocking for Lambda and other dependencies.", "dependencies": ["24.1"], "details": "Install and configure Jest, supertest, and aws-sdk. Set up environment variables and credentials for staging environment. Implement Lambda mocks for isolated API Gateway testing.", "status": "pending", "testStrategy": "Verify environment setup by running a sample test and confirming connectivity to the staging API and correct mock behavior."}, {"id": 3, "title": "Implement automated integration tests for functional and error scenarios", "description": "Develop test scripts to cover all documented scenarios, including valid/invalid requests, error handling, and edge cases for each API route.", "dependencies": ["24.2"], "details": "Write Jest test suites using supertest for HTTP requests and aws-sdk for direct API calls. Include tests for POST / with valid/invalid emails, GET /health, rate limiting, and error responses.", "status": "pending", "testStrategy": "Run the test suite and ensure all functional and error scenarios are executed, with assertions matching expected outcomes."}, {"id": 4, "title": "Test security, compliance, and infrastructure features", "description": "Create and execute tests for security-related features such as CORS headers, SSL certificate validation, and DNS resolution.", "dependencies": ["24.3"], "details": "Add tests to verify CORS headers in responses, validate SSL certificates for secure connections, and confirm DNS resolution for API endpoints.", "status": "pending", "testStrategy": "Automate security and infrastructure tests within the suite and validate results against security requirements."}, {"id": 5, "title": "Integrate tests into CI/CD pipeline and monitor coverage/performance", "description": "Configure the CI/CD pipeline to run the full integration test suite, monitor code coverage, and include performance checks for response times.", "dependencies": ["24.4"], "details": "Set up CI/CD jobs to execute tests on each deployment to staging. Ensure code coverage reports are generated and performance tests verify <200ms response time.", "status": "pending", "testStrategy": "Review CI/CD logs for test pass/fail status, coverage metrics, and performance results. Address any failures or regressions promptly."}]}, {"id": 25, "title": "Remove Function URL and cleanup legacy resources", "description": "Eliminate the old Lambda Function URL and associated Secrets Manager API key after successful migration", "details": "Remove Function URL configuration from shared-discovery-lambda. Delete associated Secrets Manager secret containing API key. Update Lambda function to remove API key validation logic if present. Remove mobile app fallback code after confirming new endpoint stability. Update documentation and deployment scripts. Consider gradual rollout: disable Function URL creation for new deployments first, then remove from existing deployments.", "testStrategy": "Verify Function URL is no longer accessible and returns 404/403. Confirm Secrets Manager secret is deleted. Test mobile app works without fallback code. Monitor error rates during transition period.", "priority": "medium", "dependencies": [24], "status": "pending", "subtasks": [{"id": 1, "title": "Remove Lambda Function URL configuration", "description": "Delete the Function URL configuration from the shared-discovery-lambda to eliminate the public HTTP endpoint.", "dependencies": [], "details": "Use the AWS Lambda console or AWS CLI 'delete-function-url-config' command to remove the Function URL from the Lambda function. Confirm that the function is no longer accessible via its previous URL.", "status": "pending", "testStrategy": "Verify that accessing the old Function URL returns a 404 or 403 error and that the URL is no longer listed in the Lambda configuration."}, {"id": 2, "title": "Delete associated Secrets Manager API key", "description": "Remove the API key stored in AWS Secrets Manager that was used for the legacy Function URL.", "dependencies": ["25.1"], "details": "Identify the secret associated with the Lambda function's API key in AWS Secrets Manager and delete it. Ensure no other resources depend on this secret before deletion.", "status": "pending", "testStrategy": "Confirm the secret is no longer present in Secrets Manager and that no Lambda environment variables or code reference the deleted secret."}, {"id": 3, "title": "Update Lambda function to remove API key validation logic", "description": "Modify the Lambda function code to eliminate any logic that validates or requires the legacy API key.", "dependencies": ["25.2"], "details": "Review the Lambda function codebase for API key validation checks and remove them. Deploy the updated function and ensure it operates correctly without the API key.", "status": "pending", "testStrategy": "Test Lambda invocations to ensure requests succeed without the API key and that no validation errors occur."}, {"id": 4, "title": "Remove mobile app fallback code and confirm new endpoint stability", "description": "Delete any fallback logic in the mobile app that references the old Function URL, after verifying the new endpoint is stable.", "dependencies": ["25.3"], "details": "Coordinate with mobile app developers to remove fallback code. Monitor the new endpoint for stability before and after the change.", "status": "pending", "testStrategy": "Test the mobile app to ensure it functions correctly without the fallback and monitor error rates for anomalies."}, {"id": 5, "title": "Update documentation and deployment scripts", "description": "Revise all relevant documentation and deployment scripts to reflect the removal of the Function URL and legacy API key.", "dependencies": ["25.4"], "details": "Update internal and external documentation, deployment guides, and infrastructure-as-code scripts to remove references to the Function URL and API key. Communicate changes to stakeholders.", "status": "pending", "testStrategy": "Review documentation and scripts for accuracy and completeness. Validate that new deployments do not create Function URLs or legacy secrets."}]}, {"id": 26, "title": "Add JSON Schema validation and enhanced security", "description": "Implement strict input validation in Lambda function and consider usage plans for additional abuse protection", "details": "Add JSON Schema validation in Go Lambda using github.com/xeipuuv/gojsonschema v1.2.0+. Define schema for POST body: { type: 'object', properties: { email: { type: 'string', format: 'email', maxLength: 254 } }, required: ['email'], additionalProperties: false }. Return 400 with detailed error for invalid input. Optionally implement UsagePlan with API key distributed via Firebase Remote Config if abuse persists. Add request/response logging with structured JSON format.", "testStrategy": "Test schema validation with various invalid inputs (missing email, invalid format, extra fields). Verify 400 responses contain helpful error messages. Test performance impact of validation is minimal (<10ms overhead).", "priority": "low", "dependencies": [25], "status": "pending", "subtasks": [{"id": 1, "title": "Define JSON Schema for Input Validation", "description": "Create a strict JSON Schema for the POST body, specifying the required 'email' property with type 'string', format 'email', and maxLength 254, and disallowing additional properties.", "dependencies": [], "details": "Use the schema: { type: 'object', properties: { email: { type: 'string', format: 'email', maxLength: 254 } }, required: ['email'], additionalProperties: false }.", "status": "pending", "testStrategy": "Verify schema correctness by testing with various valid and invalid JSON payloads."}, {"id": 2, "title": "Integrate JSON Schema Validation in Go Lambda", "description": "Implement input validation in the Lambda function using github.com/xeipuuv/gojsonschema v1.2.0+ to enforce the defined schema on incoming POST requests.", "dependencies": ["26.1"], "details": "Parse the request body and validate it against the schema using gojsonschema before processing.", "status": "pending", "testStrategy": "Test Lambda with valid and invalid inputs to ensure only valid requests are processed and invalid ones are rejected."}, {"id": 3, "title": "Return Detailed 400 Error Responses for Invalid Input", "description": "Modify the Lambda to return HTTP 400 responses with detailed error messages when input validation fails.", "dependencies": ["26.2"], "details": "Format error responses to include specific validation errors for easier debugging by clients.", "status": "pending", "testStrategy": "Send invalid requests (missing email, invalid format, extra fields) and verify the response includes clear, actionable error details."}, {"id": 4, "title": "Implement Structured JSON Logging for Requests and Responses", "description": "Add structured logging to the Lambda function to log incoming requests and outgoing responses in JSON format for traceability and debugging.", "dependencies": ["26.2"], "details": "Ensure logs capture relevant request metadata, validation results, and response status.", "status": "pending", "testStrategy": "Check logs for completeness and correctness during test invocations, ensuring sensitive data is not logged."}, {"id": 5, "title": "Optionally Add Usage Plan and API Key Distribution for Abuse Protection", "description": "If abuse is detected, configure an API Gateway Usage Plan with API key enforcement and distribute keys via Firebase Remote Config.", "dependencies": ["26.3", "26.4"], "details": "Set up API Gateway Usage Plan, generate API keys, and integrate Firebase Remote Config for secure key distribution to clients.", "status": "pending", "testStrategy": "Simulate high request rates to verify rate limiting and key enforcement; confirm only clients with valid API keys can access the endpoint."}]}, {"id": 27, "title": "Implement observability and metrics export", "description": "Add Prometheus metrics export via CloudWatch EMF and enhanced monitoring capabilities", "details": "Implement CloudWatch Embedded Metric Format (EMF) in Go Lambda using aws-lambda-go/events and custom metrics struct. Export metrics: request_count, request_duration_ms, error_count by status_code, user_pool_lookups by domain. Use EMF JSON format: { '_aws': { 'Timestamp': timestamp, 'CloudWatchMetrics': [{ 'Namespace': 'DiscoveryAPI', 'Dimensions': [['Environment']], 'Metrics': [{'Name': 'RequestCount', 'Unit': 'Count'}] }] }, 'RequestCount': 1 }. Add custom dimensions for environment, version, region.", "testStrategy": "Verify custom metrics appear in CloudWatch with correct dimensions and values. Test metric aggregation and dashboard creation. Validate EMF format compliance and metric ingestion rate.", "priority": "low", "dependencies": [26], "status": "pending", "subtasks": [{"id": 1, "title": "Design custom metrics schema and EMF JSON structure", "description": "Define the metrics to be exported (request_count, request_duration_ms, error_count by status_code, user_pool_lookups by domain) and design the CloudWatch Embedded Metric Format (EMF) JSON structure, including required dimensions (environment, version, region) and namespace.", "dependencies": [], "details": "Specify the EMF JSON format according to AWS documentation, ensuring all required fields and custom dimensions are included for each metric. Confirm the schema supports aggregation and filtering in CloudWatch.", "status": "pending", "testStrategy": "Review the schema for completeness and compliance with the CloudWatch EMF specification. Validate with sample JSON payloads."}, {"id": 2, "title": "Implement metrics collection in Go Lambda", "description": "Develop logic in the Go Lambda function to collect and aggregate the defined metrics using custom structs and aws-lambda-go/events.", "dependencies": ["27.1"], "details": "Instrument Lambda handlers to capture request_count, request_duration_ms, error_count (by status_code), and user_pool_lookups (by domain) at runtime. Store metrics in custom structs for later export.", "status": "pending", "testStrategy": "Unit test metrics collection logic to ensure correct values are captured for various request scenarios."}, {"id": 3, "title": "Serialize and emit metrics in EMF-compliant logs", "description": "Serialize the collected metrics into the EMF JSON format and emit them as structured logs from the Lambda function.", "dependencies": ["27.2"], "details": "Implement serialization logic to output metrics in the required EMF structure, including the _aws block, namespace, dimensions, and metric values. Ensure logs are emitted to stdout for CloudWatch ingestion.", "status": "pending", "testStrategy": "Verify that emitted logs match the EMF specification and contain all required fields and dimensions."}, {"id": 4, "title": "Configure CloudWatch and validate metric ingestion", "description": "Ensure CloudWatch is configured to ingest EMF logs and extract metrics. Validate that custom metrics appear in the CloudWatch console with correct dimensions and values.", "dependencies": ["27.3"], "details": "Check CloudWatch log groups and metrics dashboards for the presence of exported metrics. Confirm that metrics are aggregated and filterable by custom dimensions.", "status": "pending", "testStrategy": "Create test Lambda invocations and verify metrics appear in CloudWatch with correct values and dimensions. Test metric aggregation and dashboard creation."}, {"id": 5, "title": "Implement enhanced monitoring and alerting", "description": "Set up CloudWatch dashboards and alarms for the exported metrics to enable enhanced monitoring and incident detection.", "dependencies": ["27.4"], "details": "Create dashboards visualizing key metrics and configure alarms for thresholds on request_count, error_count, and request_duration_ms. Ensure alerts are actionable and routed appropriately.", "status": "pending", "testStrategy": "Simulate metric anomalies and verify that dashboards update and alarms trigger as expected."}]}], "metadata": {"created": "2025-08-03T21:51:52.354Z", "updated": "2025-08-04T19:51:14.701Z", "description": "Tasks for master context"}}}